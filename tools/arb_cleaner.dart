import 'dart:io';
import 'dart:convert';

void main(List<String> args) {
  if (args.length != 2) {
    print('Usage: dart arb_cleaner.dart <input_arb_file> <output_arb_file>');
    exit(1);
  }

  final inputFile = args[0];
  final outputFile = args[1];

  print('🧹 Cleaning ARB file: $inputFile');
  
  try {
    // Read the ARB file
    final file = File(inputFile);
    final content = file.readAsStringSync();
    final Map<String, dynamic> arbData = json.decode(content);
    
    final Map<String, dynamic> cleanedArb = {};
    int removedCount = 0;
    int keptCount = 0;
    
    // Copy locale metadata
    cleanedArb['@@locale'] = arbData['@@locale'];
    
    // Process each entry
    arbData.forEach((key, value) {
      if (key.startsWith('@@')) {
        cleanedArb[key] = value;
        return;
      }
      
      if (key.startsWith('@')) {
        // This is metadata for a string key
        final stringKey = key.substring(1);
        if (arbData.containsKey(stringKey)) {
          final stringValue = arbData[stringKey] as String;
          if (isValidICUString(stringValue)) {
            cleanedArb[key] = value;
          }
        }
        return;
      }
      
      // This is a string entry
      if (value is String) {
        if (isValidICUString(value)) {
          cleanedArb[key] = value;
          keptCount++;
        } else {
          print('Removing invalid string: $key = "$value"');
          removedCount++;
        }
      }
    });
    
    // Write cleaned ARB file
    final outputFileHandle = File(outputFile);
    final encoder = JsonEncoder.withIndent('  ');
    final cleanedJson = encoder.convert(cleanedArb);
    outputFileHandle.writeAsStringSync(cleanedJson);
    
    print('✅ ARB file cleaned successfully!');
    print('📊 Results:');
    print('   - Kept: $keptCount valid strings');
    print('   - Removed: $removedCount invalid strings');
    print('   - Output: $outputFile');
    
  } catch (e) {
    print('❌ Error cleaning ARB file: $e');
    exit(1);
  }
}

bool isValidICUString(String value) {
  // Check for invalid patterns that break ICU
  final invalidPatterns = [
    RegExp(r'\$\{[^}]*\}'), // Dart interpolation like ${variable}
    RegExp(r'\$\{[^}]*\?\['), // Dart null-aware access like ${obj?[
    RegExp(r'\?\['), // Null-aware operators
    RegExp(r'== '), // Comparison operators
    RegExp(r'\.substring\('), // Method calls
    RegExp(r'\.error\}'), // Property access
    RegExp(r'[\[\]](?![^\{]*\})'), // Square brackets not in ICU context
  ];
  
  for (final pattern in invalidPatterns) {
    if (pattern.hasMatch(value)) {
      return false;
    }
  }
  
  // Additional checks for overly complex strings
  if (value.length > 100) return false; // Very long strings are likely technical
  if (value.contains('http://') || value.contains('https://')) return false;
  if (value.contains('Exception') || value.contains('Error:')) return false;
  
  return true;
}
