import 'dart:io';
import 'dart:convert';

/// Comprehensive string extraction tool for RailOps multi-language implementation
/// 
/// This tool scans all Dart files in the project and extracts hardcoded strings
/// that need to be localized. It categorizes strings by their usage context
/// and generates a structured output for ARB file generation.
class StringExtractor {
  // Regex patterns for different types of string usage
  static final Map<String, RegExp> patterns = {
    // Text widget strings
    'text_widgets': RegExp(
      r"Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
    ),
    
    // AppBar titles
    'app_bar_titles': RegExp(
      r"title:\s*Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
    ),
    
    // Button labels (ElevatedButton, TextButton, etc.)
    'button_labels': RegExp(
      r"(?:ElevatedButton|TextButton|OutlinedButton|IconButton|FloatingActionButton)\s*\(.*?child:\s*Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
      dotAll: true,
    ),
    
    // Form field labels and hints
    'form_labels': RegExp(
      r"(?:labelText|hintText|helperText):\s*['\"]([^'\"]+)['\"]",
      multiLine: true,
    ),
    
    // Validation messages
    'validation_messages': RegExp(
      r"validator:.*?return\s*['\"]([^'\"]+)['\"]",
      multiLine: true,
      dotAll: true,
    ),
    
    // Dialog titles and messages
    'dialog_content': RegExp(
      r"(?:AlertDialog|SimpleDialog)\s*\(.*?(?:title|content):\s*Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
      dotAll: true,
    ),
    
    // Snackbar messages
    'snackbar_messages': RegExp(
      r"SnackBar\s*\(.*?content:\s*Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
      dotAll: true,
    ),
    
    // Navigation drawer items
    'drawer_items': RegExp(
      r"ListTile\s*\(.*?title:\s*Text\s*\(\s*['\"]([^'\"]+)['\"]\s*(?:,|\))",
      multiLine: true,
      dotAll: true,
    ),
    
    // Tab labels
    'tab_labels': RegExp(
      r"Tab\s*\(.*?text:\s*['\"]([^'\"]+)['\"]",
      multiLine: true,
      dotAll: true,
    ),
    
    // Bottom navigation labels
    'bottom_nav_labels': RegExp(
      r"BottomNavigationBarItem\s*\(.*?label:\s*['\"]([^'\"]+)['\"]",
      multiLine: true,
      dotAll: true,
    ),
    
    // Error messages in try-catch blocks
    'error_messages': RegExp(
      r"throw.*?['\"]([^'\"]+)['\"]",
      multiLine: true,
    ),
    
    // Print statements (debug strings)
    'debug_strings': RegExp(
      r"print\s*\(\s*['\"]([^'\"]+)['\"]\s*\)",
      multiLine: true,
    ),
    
    // Toast messages
    'toast_messages': RegExp(
      r"Toast\.show.*?['\"]([^'\"]+)['\"]",
      multiLine: true,
    ),
  };

  /// Results structure to hold extracted strings
  static Map<String, Map<String, List<ExtractedString>>> results = {};

  /// Extract strings from a single file
  static Map<String, List<ExtractedString>> extractFromFile(String filePath) {
    final file = File(filePath);
    if (!file.existsSync()) return {};

    final content = file.readAsStringSync();
    final Map<String, List<ExtractedString>> extracted = {};

    // Skip files that already use localization
    if (content.contains('AppLocalizations.of(context)') || 
        content.contains('AppLocalizations.of(')) {
      print('Skipping already localized file: $filePath');
      return {};
    }

    for (String category in patterns.keys) {
      extracted[category] = [];
      final matches = patterns[category]!.allMatches(content);
      
      for (final match in matches) {
        String extractedText = match.group(1)!;
        
        // Skip empty strings, single characters, or obvious non-localizable content
        if (extractedText.trim().isEmpty || 
            extractedText.length == 1 ||
            _isNonLocalizable(extractedText)) {
          continue;
        }

        // Get line number for context
        int lineNumber = _getLineNumber(content, match.start);
        
        extracted[category]!.add(ExtractedString(
          text: extractedText,
          file: filePath,
          lineNumber: lineNumber,
          category: category,
          suggestedKey: _generateKey(extractedText, category),
        ));
      }
    }

    return extracted;
  }

  /// Extract strings from all Dart files in a directory
  static Future<Map<String, Map<String, List<ExtractedString>>>> extractFromDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!directory.existsSync()) {
      throw Exception('Directory does not exist: $dirPath');
    }

    final Map<String, Map<String, List<ExtractedString>>> allResults = {};

    await for (FileSystemEntity entity in directory.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        // Skip generated files and test files
        if (entity.path.contains('.g.dart') || 
            entity.path.contains('.freezed.dart') ||
            entity.path.contains('.part.dart') ||
            entity.path.contains('_test.dart') ||
            entity.path.contains('/test/') ||
            entity.path.contains('/.dart_tool/')) {
          continue;
        }

        print('Scanning: ${entity.path}');
        final extracted = extractFromFile(entity.path);
        
        if (extracted.isNotEmpty) {
          allResults[entity.path] = extracted;
        }
      }
    }

    return allResults;
  }

  /// Generate a meaningful key from extracted text
  static String _generateKey(String text, String category) {
    // Clean the text for key generation
    String cleanText = text
        .replaceAll(RegExp(r'[^a-zA-Z0-9\s]'), '')
        .trim()
        .toLowerCase();

    // Split into words and take first few words
    List<String> words = cleanText.split(RegExp(r'\s+'));
    words = words.where((word) => word.isNotEmpty).take(3).toList();

    String baseKey = words.join('_');
    
    // Add category prefix for context
    String prefix = _getCategoryPrefix(category);
    return '${prefix}_$baseKey';
  }

  /// Get prefix for category
  static String _getCategoryPrefix(String category) {
    switch (category) {
      case 'app_bar_titles': return 'title';
      case 'button_labels': return 'btn';
      case 'form_labels': return 'form';
      case 'validation_messages': return 'validation';
      case 'dialog_content': return 'dialog';
      case 'snackbar_messages': return 'snackbar';
      case 'drawer_items': return 'drawer';
      case 'tab_labels': return 'tab';
      case 'bottom_nav_labels': return 'nav';
      case 'error_messages': return 'error';
      case 'text_widgets': return 'text';
      default: return 'msg';
    }
  }

  /// Check if text should not be localized
  static bool _isNonLocalizable(String text) {
    // Skip URLs, numbers, short codes, etc.
    return RegExp(r'^https?://').hasMatch(text) ||
           RegExp(r'^\d+$').hasMatch(text) ||
           RegExp(r'^[A-Z]{2,4}$').hasMatch(text) ||
           text.contains('@') ||
           text.contains('://') ||
           text.length < 2;
  }

  /// Get line number for a match position
  static int _getLineNumber(String content, int position) {
    return content.substring(0, position).split('\n').length;
  }

  /// Generate comprehensive summary of extraction results
  static Map<String, dynamic> generateSummary(Map<String, Map<String, List<ExtractedString>>> allResults) {
    Map<String, int> categoryCounts = {};
    Map<String, Set<String>> uniqueStrings = {};
    int totalStrings = 0;
    int totalFiles = allResults.length;

    for (String filePath in allResults.keys) {
      for (String category in allResults[filePath]!.keys) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + allResults[filePath]![category]!.length;
        
        if (!uniqueStrings.containsKey(category)) {
          uniqueStrings[category] = <String>{};
        }
        
        for (ExtractedString extracted in allResults[filePath]![category]!) {
          uniqueStrings[category]!.add(extracted.text);
          totalStrings++;
        }
      }
    }

    return {
      'summary': {
        'total_files_scanned': totalFiles,
        'total_strings_found': totalStrings,
        'unique_strings_by_category': uniqueStrings.map((k, v) => MapEntry(k, v.length)),
        'strings_by_category': categoryCounts,
        'extraction_timestamp': DateTime.now().toIso8601String(),
      },
      'detailed_results': allResults.map((filePath, fileResults) => 
        MapEntry(filePath, fileResults.map((category, strings) => 
          MapEntry(category, strings.map((s) => s.toJson()).toList())
        ))
      ),
    };
  }

  /// Generate ARB entries from extracted strings
  static Map<String, dynamic> generateARBEntries(Map<String, Map<String, List<ExtractedString>>> allResults) {
    Map<String, dynamic> arbEntries = {
      '@@locale': 'en',
    };

    Set<String> usedKeys = <String>{};
    Map<String, Set<String>> uniqueStrings = {};

    // Collect unique strings by category
    for (String filePath in allResults.keys) {
      for (String category in allResults[filePath]!.keys) {
        if (!uniqueStrings.containsKey(category)) {
          uniqueStrings[category] = <String>{};
        }
        
        for (ExtractedString extracted in allResults[filePath]![category]!) {
          uniqueStrings[category]!.add(extracted.text);
        }
      }
    }

    // Generate ARB entries for unique strings
    for (String category in uniqueStrings.keys) {
      for (String text in uniqueStrings[category]!) {
        String key = _generateUniqueKey(text, category, usedKeys);
        usedKeys.add(key);
        
        arbEntries[key] = text;
        arbEntries['@$key'] = {
          'description': 'Text from $category: ${text.length > 50 ? text.substring(0, 50) + '...' : text}',
          'context': category,
        };
      }
    }

    return arbEntries;
  }

  /// Generate unique key to avoid conflicts
  static String _generateUniqueKey(String text, String category, Set<String> usedKeys) {
    String baseKey = _generateKey(text, category);
    String key = baseKey;
    int counter = 1;
    
    while (usedKeys.contains(key)) {
      key = '${baseKey}_$counter';
      counter++;
    }
    
    return key;
  }

  /// Save results to JSON file
  static Future<void> saveResults(Map<String, dynamic> results, String outputPath) async {
    final file = File(outputPath);
    await file.writeAsString(
      JsonEncoder.withIndent('  ').convert(results),
    );
    print('Results saved to: $outputPath');
  }
}

/// Class to represent an extracted string with metadata
class ExtractedString {
  final String text;
  final String file;
  final int lineNumber;
  final String category;
  final String suggestedKey;

  ExtractedString({
    required this.text,
    required this.file,
    required this.lineNumber,
    required this.category,
    required this.suggestedKey,
  });

  Map<String, dynamic> toJson() => {
    'text': text,
    'file': file,
    'line_number': lineNumber,
    'category': category,
    'suggested_key': suggestedKey,
  };
}

/// Main execution function
Future<void> main(List<String> args) async {
  print('🚀 RailOps String Extraction Tool');
  print('=====================================');
  
  String inputDir = args.isNotEmpty ? args[0] : 'lib';
  String outputFile = args.length > 1 ? args[1] : 'extracted_strings.json';
  String arbOutputFile = args.length > 2 ? args[2] : 'lib/l10n/app_en_extracted.arb';

  print('📁 Scanning directory: $inputDir');
  print('📄 Output file: $outputFile');
  print('🌐 ARB output file: $arbOutputFile');
  print('');

  try {
    // Extract strings from all Dart files
    final results = await StringExtractor.extractFromDirectory(inputDir);
    
    if (results.isEmpty) {
      print('❌ No strings found or all files are already localized.');
      return;
    }

    // Generate comprehensive summary
    final summary = StringExtractor.generateSummary(results);
    
    // Save detailed results
    await StringExtractor.saveResults(summary, outputFile);
    
    // Generate and save ARB entries
    final arbEntries = StringExtractor.generateARBEntries(results);
    await StringExtractor.saveResults(arbEntries, arbOutputFile);
    
    // Print summary
    print('✅ Extraction completed successfully!');
    print('');
    print('📊 Summary:');
    print('   Files scanned: ${summary['summary']['total_files_scanned']}');
    print('   Total strings found: ${summary['summary']['total_strings_found']}');
    print('   Unique strings: ${(summary['summary']['unique_strings_by_category'] as Map).values.fold(0, (a, b) => a + (b as int))}');
    print('');
    print('📋 Strings by category:');
    (summary['summary']['strings_by_category'] as Map).forEach((category, count) {
      print('   ${category.toString().padRight(20)}: $count strings');
    });
    print('');
    print('📁 Files generated:');
    print('   📄 Detailed results: $outputFile');
    print('   🌐 ARB file: $arbOutputFile');
    print('');
    print('🔄 Next steps:');
    print('   1. Review the generated ARB file');
    print('   2. Update lib/l10n/app_en.arb with relevant strings');
    print('   3. Run code replacement tool to replace hardcoded strings');
    
  } catch (e) {
    print('❌ Error during extraction: $e');
    exit(1);
  }
}
